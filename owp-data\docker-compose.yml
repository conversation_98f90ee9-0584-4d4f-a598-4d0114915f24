version: '3.8'

services:
  owp-data:
    build:
      context: ./docker/dev
      dockerfile: Dockerfile
    container_name: owp-data
    # 使用host网络模式的替代方案 - 映射所有必要端口
    ports:
      - "9004:9004"  # 应用端口
      - "9102:9102"  # 波浪雷达端口
      - "9104:9104"  # 动态V2控制端口
      - "9106:9106"  # 海流计端口
    # 添加额外的hosts映射，将远程服务IP映射到容器内
    extra_hosts:
      - "owp-wind:**********"
      - "owp-file:***********"
      - "owp-gateway:**********"
      - "owp-user:***********"
      - "owp-monitor:***********"  # monitor服务在宿主机上
      - "nacos-server:************"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - JAVA_OPTS=-Xms512m -Xmx1024m
      # 设置Nacos服务发现的IP为宿主机IP，这样其他服务可以访问到
      - SPRING_CLOUD_NACOS_DISCOVERY_IP=***********
    volumes:
      - ./target/owp-data.jar:/app/app.jar
      - ./logs:/logs
      - ./data:/data
    restart: unless-stopped
    # 使用自定义网络
    networks:
      - owp-network

  # TDengine 数据库服务
  tdengine:
    image: tdengine/tdengine:*******
    container_name: tdengine
    ports:
      - "6030:6030"
      - "6041:6041"
      - "6043-6049:6043-6049"
      - "6043-6049:6043-6049/udp"
    environment:
      - TAOS_FQDN=tdengine
    volumes:
      - tdengine-data:/var/lib/taos
      - tdengine-log:/var/log/taos
    networks:
      - owp-network

networks:
  owp-network:
    driver: bridge
    ipam:
      config:
        # 使用与远程服务不冲突的网段
        - subnet: **********/16

volumes:
  tdengine-data:
  tdengine-log:
